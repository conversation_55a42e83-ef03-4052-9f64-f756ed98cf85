<template>
    <DashboardLayout title="Dashboard">
        <SettingsPanel>
            <div class="my-48 max-w-6xl">
                <form @submit.prevent="submitProfile">
                    <h2 class="fs-md mb-20 font-bold text-text-headings">
                        Profile details
                    </h2>
                    <div class="gap-x-32 md:grid md:grid-cols-2 xl:grid-cols-3">
                        <TextInput
                            id="first_name"
                            name="first_name"
                            v-model="profileForm.first_name"
                            label="First name"
                            type="text"
                            required
                            :rules="[rules.required]"
                            :serverError="profileForm.errors.first_name"
                        />

                        <TextInput
                            id="last_name"
                            v-model="profileForm.last_name"
                            name="last_name"
                            label="Last name"
                            type="text"
                            required
                            :rules="[rules.required]"
                            :serverError="profileForm.errors.last_name"
                        ></TextInput>

                        <TextInput
                            id="phone"
                            v-model="profileForm.phone"
                            name="phone"
                            label="Phone number"
                            type="text"
                            :serverError="profileForm.errors.phone"
                        ></TextInput>

                        <TextInput
                            id="email"
                            v-model="profileForm.email"
                            name="email"
                            label="Email address"
                            type="email"
                            required
                            :rules="[rules.required, rules.email]"
                            :serverError="profileForm.errors.email"
                        ></TextInput>
                    </div>

                    <Button type="submit" color="action" class="h-48 min-w-80">
                        Save
                    </Button>
                </form>

                <div class="mt-24 border-t border-border-primary pt-24">
                    <h2 class="fs-md mb-20 font-bold text-text-headings">
                        Organisation details
                    </h2>

                    <div class="mb-24">
                        <div class="fs-md inline-block border-b border-border-primary text-center w-full">
                            <ul class="flex flex-wrap">
                                <li
                                    v-for="tab in organizationTabs"
                                    :key="tab.id"
                                    class="me-2"
                                >
                                    <button
                                        @click="activeOrgTab = tab.id"
                                        :class="[
                                            'inline-block rounded-t-lg border-b-2 px-24 py-12 transition',
                                            activeOrgTab === tab.id
                                                ? 'border-border-action font-bold text-text-action-hover'
                                                : 'border-transparent hover:border-border-action hover:text-text-action-hover'
                                        ]"
                                    >
                                        {{ tab.name }}
                                    </button>
                                </li>
                            </ul>
                        </div>

                        <!-- Name Tab -->
                        <div v-if="activeOrgTab === 'name'" class="mt-24">
                            <div>
                                <TextInput
                                    id="name"
                                    v-model="unifiedOrgForm.name"
                                    name="name"
                                    label="Organisation name"
                                    type="text"
                                    required
                                    :rules="[rules.required]"
                                    :serverError="unifiedOrgForm.errors.name"
                                ></TextInput>
                            </div>
                        </div>

                        <!-- Mission Tab -->
                        <div v-if="activeOrgTab === 'mission'" class="mt-24">
                            <div>
                                <TextArea
                                    v-model="unifiedOrgForm.about"
                                    id="about"
                                    name="about"
                                    label="Let us know how you help and where you do it"
                                    rows="6"
                                    required
                                    :rules="[rules.required]"
                                    :serverError="unifiedOrgForm.errors.about"
                                    maxlength="600"
                                ></TextArea>

                                <div class="mb-20 text-text-disabled">
                                    {{ missionCharCount }} / 600
                                </div>
                            </div>
                        </div>

                        <!-- Region Tab -->
                        <div v-if="activeOrgTab === 'region'" class="mt-24">
                            <div>
                                <TextInput
                                    id="region"
                                    v-model="unifiedOrgForm.region"
                                    name="region"
                                    label="National, North West England, Durham...you get the idea"
                                    type="text"
                                    required
                                    :rules="[rules.required]"
                                    :serverError="unifiedOrgForm.errors.region"
                                ></TextInput>
                            </div>
                        </div>

                        <!-- Language Tab -->
                        <div v-if="activeOrgTab === 'language'" class="mt-24">
                            <div>
                                <TextArea
                                    v-model="unifiedOrgForm.language_preference"
                                    id="language_preference"
                                    name="language_preference"
                                    label="For e.g. people who find themselves homeless rather than the homeless"
                                    rows="6"
                                    :serverError="unifiedOrgForm.errors.language_preference"
                                    maxlength="255"
                                ></TextArea>

                                <div class="mb-20 text-text-disabled">
                                    {{ languageCharCount }} / 255
                                </div>
                            </div>
                        </div>

                        <!-- Tone Tab -->
                        <div v-if="activeOrgTab === 'tone'" class="mt-24">
                            <div>
                                <TextArea
                                    v-model="unifiedOrgForm.tone_preference"
                                    id="tone_preference"
                                    name="tone_preference"
                                    label="For e.g. empathetic without making people feel guilty"
                                    rows="6"
                                    :serverError="unifiedOrgForm.errors.tone_preference"
                                    maxlength="255"
                                ></TextArea>

                                <div class="mb-20 text-text-disabled">
                                    {{ toneCharCount }} / 255
                                </div>
                            </div>
                        </div>

                        <!-- Category Tab -->
                        <div v-if="activeOrgTab === 'category'" class="mt-24">
                            <div class="space-y-24">
                                <SelectInput
                                    id="settings_general_category"
                                    label="General category"
                                    labelPosition="outside"
                                    class="w-full"
                                    :options="findDonorsStore.generalCategoryOptions"
                                    v-model="unifiedOrgForm.general_category"
                                    @change="handleGeneralCategoryChange"
                                    :disabled="findDonorsStore.generalCategoryOptions.length < 1"
                                    :serverError="unifiedOrgForm.errors.general_category"
                                />

                                <SelectInput
                                    id="settings_sub_category"
                                    label="Sub-category"
                                    labelPosition="outside"
                                    class="w-full"
                                    :options="findDonorsStore.subCategoryOptions"
                                    v-model="unifiedOrgForm.sub_category"
                                    :disabled="findDonorsStore.subCategoryOptions.length < 1"
                                    :serverError="unifiedOrgForm.errors.sub_category"
                                />
                            </div>
                        </div>

                        <div class="mt-24 pt-24 border-t border-border-primary">
                            <Button
                                @click="submitAllOrganizationData"
                                color="action"
                                class="h-48 min-w-120"
                                :disabled="unifiedOrgForm.processing"
                            >
                                <span v-if="unifiedOrgForm.processing">Saving...</span>
                                <span v-else>Save</span>
                            </Button>
                        </div>
                    </div>
                </div>

                <form
                    @submit.prevent="submitPassword"
                    class="mt-24 border-t border-border-primary pt-24"
                >
                    <h2 class="fs-md mb-20 font-bold text-text-headings">
                        Change password
                    </h2>
                    <div class="gap-x-32 md:grid md:grid-cols-2 xl:grid-cols-3">
                        <TextInput
                            id="current_password"
                            v-model="passwordForm.current_password"
                            name="current_password"
                            label="Current password"
                            type="password"
                            :serverError="passwordForm.errors.current_password"
                        ></TextInput>

                        <TextInput
                            id="new_password"
                            v-model="passwordForm.new_password"
                            name="new_password"
                            label="New password"
                            type="password"
                            :rules="[rules.password]"
                            :serverError="passwordForm.errors.new_password"
                        ></TextInput>

                        <TextInput
                            id="confirm_new_password"
                            v-model="passwordForm.confirm_new_password"
                            name="confirm_new_password"
                            label="Confirm new password"
                            type="password"
                            :rules="[rules.password]"
                            :serverError="
                                passwordForm.errors.confirm_new_password
                            "
                        ></TextInput>
                    </div>

                    <Button
                        type="submit"
                        color="action"
                        class="h-48 min-w-80"
                        :disabled="profileForm.processing"
                    >
                        Save
                    </Button>
                </form>

                <Toast
                    v-if="toast.message"
                    :text="toast.message"
                    :type="toast.type"
                    @handleClose="toast.message = ''"
                ></Toast>
            </div>
        </SettingsPanel>
    </DashboardLayout>
</template>

<script setup>
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import SettingsPanel from "@/Components/Dashboard/Settings/SettingsPanel.vue";

import TextInput from "@/Components/TextInput/TextInput.vue";
import rules from "@/utilities/validation-rules";
import { useForm, usePage } from "@inertiajs/vue3";
import Button from "@/Components/Button/Button.vue";
import { ref, computed, onMounted, watch } from "vue";
import TextArea from "@/Components/TextArea/TextArea.vue";
import Toast from "@/Components/Toast/Toast.vue";
import SelectInput from "@/Components/SelectInput/SelectInput.vue";
import { fetchGeneralCategories, fetchSubCategories } from "@/utilities/api";
import { useFindDonorsStore } from "@/stores/findDonors";

const { auth, companyAbout, companyName, companyRegion, companyLanguagePreference, companyTonePreference, companyGeneralCategory, companySubCategory } = defineProps({
    auth: Object,
    companyAbout: String,
    companyName: String,
    companyRegion: String,
    companyLanguagePreference: String,
    companyTonePreference: String,
    companyGeneralCategory: String,
    companySubCategory: String
});

const profileForm = useForm({
    first_name: auth.user.first_name || "",
    last_name: auth.user.last_name || "",
    email: auth.user.email || "",
    phone: auth.user.phone || "",
});

const submitProfile = () => {
    profileForm.post(route("settings.profile.update"), {
        onFinish: (res) => console.log(res),
        onSuccess: () => {
            toast.value.message = "Profile details saved successfully";
            toast.value.type = "success";
        },
        onError: () => {
            toast.value.message = "Error saving your profile details";
            toast.value.type = "danger";
        },
    });
};

const organizationTabs = [
    { id: 'name', name: 'Name' },
    { id: 'mission', name: 'Mission' },
    { id: 'region', name: 'Region' },
    { id: 'language', name: 'Language' },
    { id: 'tone', name: 'Tone' },
    { id: 'category', name: 'Cause' }
];

const activeOrgTab = ref('name');

const unifiedOrgForm = useForm({
    name: companyName || "",
    about: companyAbout || "",
    region: companyRegion || "",
    language_preference: companyLanguagePreference || "",
    tone_preference: companyTonePreference || "",
    general_category: companyGeneralCategory || "",
    sub_category: companySubCategory || "",
});

const missionCharCount = computed(() => {
    return unifiedOrgForm.about.length;
});

const languageCharCount = computed(() => {
    return unifiedOrgForm.language_preference.length;
});

const toneCharCount = computed(() => {
    return unifiedOrgForm.tone_preference.length;
});

const submitAllOrganizationData = async () => {
    unifiedOrgForm.processing = true;

    try {
        unifiedOrgForm.clearErrors();

        const companyFormData = useForm({
            name: unifiedOrgForm.name,
        });

        await new Promise((resolve, reject) => {
            companyFormData.post(route("api.setup.company"), {
                onSuccess: () => resolve(),
                onError: (errors) => {
                    if (errors.name) {
                        unifiedOrgForm.errors.name = errors.name;
                    }
                    reject(errors);
                },
            });
        });


        const aboutFormData = useForm({
            about: unifiedOrgForm.about,
            region: unifiedOrgForm.region,
            language_preference: unifiedOrgForm.language_preference,
            tone_preference: unifiedOrgForm.tone_preference,
            general_category: unifiedOrgForm.general_category,
            sub_category: unifiedOrgForm.sub_category,
        });

        await new Promise((resolve, reject) => {
            aboutFormData.post(route("api.setup.about"), {
                onSuccess: () => resolve(),
                onError: (errors) => {
                    // Copy about errors to unified form
                    Object.keys(errors).forEach(key => {
                        if (errors[key]) {
                            unifiedOrgForm.errors[key] = errors[key];
                        }
                    });
                    reject(errors);
                },
            });
        });

        toast.value.message = "All organisation details saved successfully";
        toast.value.type = "success";

    } catch (error) {
        toast.value.message = "Error saving organisation details. Please check the form for errors.";
        toast.value.type = "danger";
        console.error('Organization save error:', error);
    } finally {
        unifiedOrgForm.processing = false;
    }
};

const passwordForm = useForm({
    current_password: "",
    new_password: "",
    confirm_new_password: "",
});

const submitPassword = () => {
    passwordForm.post(route("settings.profile.password"), {
        onFinish: (res) => console.log(res),
        onSuccess: (res) => {
            passwordForm.reset();
            toast.value.message = "Password successfully updated";
            toast.value.type = "success";
        },
        onError: () => {
            toast.value.message = "Error updating your password";
            toast.value.type = "danger";
        },
    });
};

//
// Toast handlers
// ==========================================================================

const page = usePage();

onMounted(() => {
    if (page.props.jetstream.flash?.message) {
        toast.value.message = page.props.jetstream.flash?.message;
        toast.value.type = "success";
    }

    if (!findDonorsStore.generalCategoryOptions.length) {
        fetchGeneralCategories();
    }

    if (companyGeneralCategory) {
        fetchSubCategories(companyGeneralCategory).then(() => {
            if (companySubCategory) {
                unifiedOrgForm.sub_category = companySubCategory;
            }
        });
    }
});

const toast = ref({
    message: "",
    type: "",
});

const findDonorsStore = useFindDonorsStore();
const { setSubCategoryOptions } = findDonorsStore;

const handleGeneralCategoryChange = async () => {
    setSubCategoryOptions([]);
    unifiedOrgForm.sub_category = null;
    if (unifiedOrgForm.general_category) {
        await fetchSubCategories(unifiedOrgForm.general_category);
    }
};

let isInitialLoad = true;
watch(
    () => unifiedOrgForm.general_category,
    async () => {
        if (isInitialLoad) {
            isInitialLoad = false;
            return;
        }
        await handleGeneralCategoryChange();
    }
);

watch(
    () => findDonorsStore.generalCategoryOptions,
    (newOptions) => {
        if (newOptions.length > 0 && companyGeneralCategory && !unifiedOrgForm.general_category) {
            unifiedOrgForm.general_category = companyGeneralCategory;
        }
    }
);
</script>
