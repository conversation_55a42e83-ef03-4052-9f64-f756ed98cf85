<template>

    <Head title="Organisation preferred language">
        <meta name="description" content="Add your preferred language to your Pravi account." />
    </Head>

    <OnboardingLayout :currentStep="{ step: 5, sub: 1 }">
        <div class="space-y-48">
            <p>To help us generate content for you</p>

            <div class="space-y-8">
                <h1 class="fs-2xl font-bold">
                    Tell us about your preferred language
                </h1>
                <p class="fs-xs font-medium text-text-grey">
                    You can tell about this later
                </p>
            </div>
        </div>

        <form class="flex flex-col h-full justify-between mt-24" @submit.prevent="submit">
            <div>
                <TextArea v-model="form.language_preference" id="language_preference" name="language_preference"
                    label="For e.g. people who find themselves homeless rather than the homeless" rows="6"
                    :serverError="form.errors.language_preference" maxlength="255"></TextArea>
                <div class="mb-4 text-text-disabled">{{ charCount }} / 255</div>
            </div>

            <div class="flex justify-between">
                <button @click.prevent="back" class="btn--secondary" :disabled="form.processing">
                    <IconArrowLeft />
                    Back
                </button>

                <button type="submit" class="btn--secondary group" :disabled="form.processing">
                    Continue

                    <IconArrowRight class="stroke-text-action-hover group-disabled:stroke-text-disabled" />
                </button>
            </div>
        </form>

        <template v-slot:image>
            <img class="h-auto w-auto max-w-[500px] lg:max-h-full xl:max-w-[685px] xl:-translate-x-80"
                src="/images/onboarding/onboarding-4.png" alt="" width="685" height="800" />
        </template>
    </OnboardingLayout>
</template>

<script setup>
import { Head, useForm, router } from "@inertiajs/vue3";
import OnboardingLayout from "@/Layouts/OnboardingLayout.vue";
import IconArrowRight from "@/Components/Icons/IconArrowRight.vue";
import IconArrowLeft from "@/Components/Icons/IconArrowLeft.vue";
import TextArea from "@/Components/TextArea/TextArea.vue";
import { computed } from "vue";

const props = defineProps({
    language_preference: String,
});

const form = useForm({
    language_preference: props.language_preference ?? "",
});

const charCount = computed(() => {
    return form.language_preference.length;
});

const submit = () => {
    form.post(route("api.setup.about"), {
        onSuccess: () => {
            router.get("/setup/organisation-tone");
        },
        onError: (err) => {
            console.log(err);
        },
    });
};

const back = () => {
    router.get("/setup/organisation-region");
};
</script>
