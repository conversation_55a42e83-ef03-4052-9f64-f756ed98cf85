<template>

    <Head title="Organisation mission">
        <meta name="description" content="Add your organisation mission to your Pravi account." />
    </Head>

    <OnboardingLayout :currentStep="{ step: 2, sub: 1 }">
        <div class="space-y-48">
            <p>To help us generate content for you</p>
            <h1 class="fs-2xl font-bold after:content-['*'] after:text-text-action">
                Describe your organisation's mission
            </h1>
        </div>

        <form class="flex flex-col h-full justify-between mt-24" @submit.prevent="submit">
            <div>
                <TextArea v-model="form.about" id="about" name="about"
                    label="Let us know how you help and where you do it" rows="6" required :rules="[rules.required]"
                    :serverError="form.errors.about" maxlength="600"></TextArea>
                <span class="mb-8 text-text-disabled">{{ charCount }} / 600</span>
            </div>

            <div class="mt-24 flex justify-between">
                <button @click.prevent="back" class="btn--secondary" :disabled="form.processing">
                    <IconArrowLeft />
                    Back
                </button>

                <button type="submit" class="btn--secondary group" :disabled="form.processing || !form.about">
                    Continue

                    <IconArrowRight class="stroke-text-action-hover group-disabled:stroke-text-disabled" />
                </button>
            </div>
        </form>

        <template v-slot:image>
            <img class="h-auto w-auto max-w-[600px] lg:max-h-full xl:max-w-[800px] xl:-translate-x-64"
                src="/images/onboarding/onboarding-2.png" alt="" width="800" height="625" />
        </template>
    </OnboardingLayout>
</template>

<script setup>
import { Head, useForm, router } from "@inertiajs/vue3";
import OnboardingLayout from "@/Layouts/OnboardingLayout.vue";
import IconArrowRight from "@/Components/Icons/IconArrowRight.vue";
import { computed } from "vue";
import TextArea from "@/Components/TextArea/TextArea.vue";
import rules from "@/utilities/validation-rules";
import IconArrowLeft from "@/Components/Icons/IconArrowLeft.vue";

const props = defineProps({
    about: String,
});

const form = useForm({
    about: props.about ?? "",
});

const charCount = computed(() => {
    return form.about.length;
});

const submit = () => {
    form.post(route("api.setup.about"), {
        onSuccess: () => {
            router.get("/setup/organisation-cause");
        },
        onError: (err) => {
            console.log(err);
        },
    });
};

const back = () => {
    router.get("/setup/organisation-name");
};
</script>
