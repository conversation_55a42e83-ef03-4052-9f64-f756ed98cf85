<template>

    <Head title="Organisation preferred tone of voice">
        <meta name="description" content="Add your preferred tone of voice to your Pravi account." />
    </Head>

    <OnboardingLayout :currentStep="{ step: 6, sub: 1 }">
        <div class="space-y-48">
            <p>To help us generate content for you</p>
            <div class="space-y-8">
                <h1 class="fs-2xl font-bold">
                    Let us know about your preferred tone of voice
                </h1>
                <p class="fs-xs font-medium text-text-grey">
                    You can tell about this later
                </p>
            </div>
        </div>

        <form class="flex flex-col h-full justify-between mt-24" @submit.prevent="submit">
            <div>
                <TextArea v-model="form.tone_preference" id="tone_preference" name="tone_preference"
                    label="For e.g. empathetic without making people feel guilty" rows="6"
                    :serverError="form.errors.tone_preference" maxlength="255"></TextArea>
                <span class="text-text-disabled">{{ charCount }} / 255</span>
            </div>

            <div class="flex justify-between">
                <button @click.prevent="back" class="btn--secondary" :disabled="form.processing">
                    <IconArrowLeft />
                    Back
                </button>

                <button type="submit" class="btn--secondary" :disabled="form.processing">
                    Continue

                    <IconArrowRight class="stroke-text-action-hover group-disabled:stroke-text-disabled" />
                </button>
            </div>
        </form>

        <template v-slot:image>
            <img class="h-auto w-auto max-w-[450px] lg:max-h-full xl:max-w-[600px] xl:-translate-x-64"
                src="/images/onboarding/onboarding-5.png" alt="" width="600" height="800" />
        </template>
    </OnboardingLayout>
</template>

<script setup>
import { Head, useForm, router } from "@inertiajs/vue3";
import OnboardingLayout from "@/Layouts/OnboardingLayout.vue";
import IconArrowRight from "@/Components/Icons/IconArrowRight.vue";
import IconArrowLeft from "@/Components/Icons/IconArrowLeft.vue";
import TextArea from "@/Components/TextArea/TextArea.vue";
import { computed } from "vue";

const props = defineProps({
    tone_preference: String,
});

const form = useForm({
    tone_preference: props.tone_preference ?? "",
});

const charCount = computed(() => {
    return form.tone_preference.length;
});

const submit = () => {
    form.post(route("api.setup.about"), {
        onSuccess: () => {
            router.get("/build/campaign");
        },
        onError: (err) => {
            console.log(err);
        },
    });
};

const back = () => {
    router.get("/setup/organisation-language");
};
</script>
