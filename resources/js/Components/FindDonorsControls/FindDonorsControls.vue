<template>
    <ConversationPravi fullWidth>
        <h2
            class="fs-md mb-32 inline-block rounded-3xl bg-surface-action-2x-light px-20 py-12"
        >
            Select the cause that best represents your organization below
        </h2>

        <form @submit.prevent="submit">
            <div
                class="flex flex-col justify-between gap-0 md:flex-row md:gap-16"
            >
                <SelectInput
                    id="find_donors_general_category"
                    label="General category"
                    labelPosition="outside"
                    class="w-full"
                    :options="findDonorsStore.generalCategoryOptions"
                    v-model="findDonorsStore.form.GENERAL_CATEGORY"
                    @change="fetchSubCategories"
                    :disabled="
                        findDonorsStore.generalCategoryOptions.length < 1
                    "
                    :serverError="errors.GENERAL_CATEGORY"
                />
                <SelectInput
                    id="find_donors_sub_category"
                    label="Sub-category"
                    labelPosition="outside"
                    class="w-full"
                    :options="findDonorsStore.subCategoryOptions"
                    v-model="findDonorsStore.form.SUB_CATEGORY"
                    @change="fetchAreaOfFocus"
                    :disabled="findDonorsStore.subCategoryOptions.length < 1"
                    :serverError="errors.SUB_CATEGORY"
                />
                <SelectInputName
                    label="Location"
                    labelPosition="outside"
                    class="w-full"
                    :options="optionsLocation"
                    v-model="findDonorsStore.form.LOCATION"
                    :serverError="errors.LOCATION"
                />
            </div>

            <div class="mt-24 md:mt-40">
                <div class="flex flex-col gap-16 sm:flex-row">
                    <Button
                        @click="handleClearButton"
                        color="default"
                        :disabled="findDonorsStore.isLoading"
                    >
                        Clear selection
                    </Button>

                    <Button
                        type="submit"
                        color="action"
                        :disabled="findDonorsStore.isLoading"
                    >
                        Create donor personas
                    </Button>
                </div>
            </div>
        </form>
    </ConversationPravi>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import Button from "../Button/Button.vue";
import ConversationPravi from "../ConversationPravi/ConversationPravi.vue";
import SelectInput from "../SelectInput/SelectInput.vue";
import { arrayToSelectInput } from "@/utilities/helpers";
import { useFindDonorsStore } from "@/stores/findDonors";
import { submitFindDonors } from "@/utilities/api";
import SelectInputName from "../SelectInput/SelectInputName.vue";

const props = defineProps(["optionsLocation"]);

const emit = defineEmits([
    "handleNextStep",
    "handlePreviousStep",
    "handleNextText",
]);

const findDonorsStore = useFindDonorsStore();

const {
    setGeneralCategoryOptions,
    setSubCategoryOptions,
    setIsLoading,
    clearForm,
    setDonorsResults,
    updateForm,
    clearFormPersonaData,
    setShownResultIndex,
} = findDonorsStore;

const errors = ref({});

// Fetch general categories only if they aren't already in the store
const fetchGeneralCategories = async () => {
    // Reset sub-categories and related fields
    setSubCategoryOptions([]);
    updateForm({ SUB_CATEGORY: null });

    try {
        const response = await axios.get("/data/charity-categories");
        setGeneralCategoryOptions(arrayToSelectInput(response.data));
    } catch (error) {
        console.error("Error fetching general categories:", error);
    }
};

// Always fetch sub-categories whenever a general category is selected
const fetchSubCategories = async () => {
    if (!findDonorsStore.form.GENERAL_CATEGORY) return;

    try {
        const response = await axios.post("/data/charity-sub-categories", {
            GENERAL_CATEGORY: findDonorsStore.form.GENERAL_CATEGORY,
        });
        if (response.data.length) {
            setSubCategoryOptions(arrayToSelectInput(response.data));
        }
    } catch (error) {
        console.error("Error fetching sub-categories:", error);
    }
};

// Instead of a separate watcher that only resets, combine reset and fetch for sub-categories:
watch(
    () => findDonorsStore.form.GENERAL_CATEGORY,
    async (newVal, oldVal) => {
        // Reset sub-category values immediately
        setSubCategoryOptions([]);
        updateForm({ SUB_CATEGORY: null });
        // Always fetch sub-categories when GENERAL_CATEGORY changes
        await fetchSubCategories();
    },
);

// Fetch the general categories on component mount if they are not already available.
onMounted(() => {
    if (!findDonorsStore.generalCategoryOptions.length) {
        fetchGeneralCategories();
    }
});

// Final action submit handlers
const handleSuccess = () => {
    emit("handleNextStep"); // Emit when the form submission succeeds
    setIsLoading(false);
};

const handleError = (errorData) => {
    errors.value = errorData; // Handle errors in this component
    console.error("Error finding donors:", errorData);
    setIsLoading(false);
};

const submit = () => {
    setShownResultIndex(0);
    clearFormPersonaData();
    emit("handlePreviousStep");
    errors.value = {};
    setDonorsResults(null);
    setIsLoading(true);
    emit("handleNextText");
    findDonorsStore.setIsCustomPrediction(false);

    submitFindDonors(findDonorsStore.form, handleSuccess, handleError);
};

const handleClearButton = () => {
    clearForm();
    setSubCategoryOptions([]);
    fetchGeneralCategories();
    setDonorsResults(null);
    emit("handlePreviousStep");
};
</script>
